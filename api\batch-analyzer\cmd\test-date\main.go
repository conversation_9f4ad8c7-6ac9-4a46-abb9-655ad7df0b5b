package main

import (
	"fmt"
	"log"
	batch_analyzer "lottery/batch-analyzer"
)

func main() {
	log.Println("測試日期解析功能...")

	// 創建服務實例
	service := batch_analyzer.NewBatchAnalysisService()

	// 測試不同格式的日期字符串
	testDates := []string{
		"2025-08-22",                // 標準日期格式
		"2025-08-22T00:00:00+08:00", // RFC3339 格式（錯誤中的格式）
		"2025-08-22 00:00:00",       // MySQL datetime 格式
		"2025/08/22",                // 斜線分隔格式
		"",                          // 空字符串
		"invalid-date",              // 無效格式
	}

	for i, dateStr := range testDates {
		fmt.Printf("\n測試 %d: %s\n", i+1, dateStr)

		if date, err := service.ParseLottoDrawDate(dateStr); err != nil {
			fmt.Printf("  ❌ 解析失敗: %v\n", err)
		} else {
			fmt.Printf("  ✅ 解析成功: %s\n", date.Format("2006-01-02"))
		}
	}

	log.Println("\n日期解析測試完成")
}
